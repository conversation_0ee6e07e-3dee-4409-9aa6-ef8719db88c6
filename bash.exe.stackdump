Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x1FE8E
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210286019, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DD0  000210068E24 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0B0  00021006A225 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9195C0000 ntdll.dll
7FF918770000 KERNEL32.DLL
7FF916C90000 KERNELBASE.dll
7FF917FD0000 USER32.dll
7FF917270000 win32u.dll
7FF918220000 GDI32.dll
7FF917130000 gdi32full.dll
7FF917080000 msvcp_win.dll
000210040000 msys-2.0.dll
7FF916710000 ucrtbase.dll
7FF918CD0000 advapi32.dll
7FF917D30000 msvcrt.dll
7FF9186C0000 sechost.dll
7FF9184F0000 RPCRT4.dll
7FF915E10000 CRYPTBASE.DLL
7FF916A70000 bcryptPrimitives.dll
7FF917E80000 IMM32.DLL
