.event-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.event-form {
  // .card {
  //   transition: transform 0.2s;
  //   border-radius: 8px;

  //   &:hover {
  //     transform: translateY(-2px);
  //   }
  // }

  .form-label {
    font-weight: 500;
    color: #333;
  }

  .text-danger {
    color: #dc3545 !important;
  }

  .upload-container {
    background-color: #f8f9fa;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #e9ecef;
    }
  }

  .image-preview img {
    object-fit: cover;
    border-radius: 4px;
  }

  .form-control,
  .form-select {
    border-color: #ced4da;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }
  }

  .form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
  }

  .btn-outline-primary {
    color: #dc3545;
    border-color: #dc3545;

    &:hover,
    &:active,
    &.active {
      background-color: #dc3545;
      border-color: #dc3545;
      color: white;
    }
  }

  .btn-check:checked + .btn-outline-primary {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }

  // Location tabs styling
  .location-tabs {
    .tab-container {
      display: flex;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
      max-width: 400px;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        cursor: pointer;
        background-color: #fff;
        transition:
          background-color 0.2s,
          color 0.2s;
        font-size: 14px;

        &:not(:last-child) {
          border-right: 1px solid #dee2e6;
        }

        &.active {
          background-color: #f0f0f0;
          font-weight: 500;
          color: #333;
        }

        &:hover:not(.active) {
          background-color: #f8f9fa;
        }
      }
    }
  }
}

// PrimeNG component styling
:host ::ng-deep {
  // Ensure consistent 39px height for all input fields with green focus effect
  .p-inputtext {
    height: 39px !important;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;

    &:focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }

    &:hover:not(:focus) {
      border-color: #28a745 !important;
    }
  }

  // Textarea styling with consistent green focus effect
  .p-textarea {
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;

    &:focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }

    &:hover:not(:focus) {
      border-color: #28a745 !important;
    }
  }

  // Checkbox and radio button alignment with green focus effect
  .p-checkbox,
  .p-radiobutton {
    display: flex;
    align-items: center;

    .p-checkbox-box,
    .p-radiobutton-box {
      margin-top: 0;
      vertical-align: middle;
      border: 1px solid #ced4da !important;

      &:hover {
        border-color: #28a745 !important;
      }

      &.p-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
      }

      &.p-highlight {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
      }
    }
  }
  // SelectButton styling for location type
  .p-selectbutton {
    .p-button {
      background-color: #fff;
      color: #6c757d;
      border: 1px solid #ced4da;
      transition:
        background-color 0.2s,
        color 0.2s,
        border-color 0.2s;

      &:not(:last-child) {
        border-right: 0;
      }

      &.p-highlight {
        background-color: #dc3545;
        color: #fff;
        border-color: #dc3545;
      }

      &:hover:not(.p-highlight) {
        background-color: #f8f9fa;
      }
    }
  }

  // Calendar styling for date picker triggers
  .p-calendar {
    .p-datepicker-trigger {
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #bb2d3b;
        border-color: #bb2d3b;
      }
    }
  }

  // Standardized button styling to match user list component
  .p-button {
    border-radius: 4px;
    padding: 0.75rem 1.25rem !important;
    height: auto !important;
    min-width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .p-button-outlined.p-button-danger {
    height: 45px !important;
    background-color: white !important;
    color: #dc3545 !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: rgba(220, 53, 69, 0.04) !important;
      border-color: #c82333 !important;
      color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }

  .p-button-danger:not(.p-button-outlined) {
    height: 45px !important;
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: #c82333 !important;
      border-color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }

  .p-datepicker {
    .p-datepicker-header {
      .p-datepicker-title {
        .p-datepicker-month,
        .p-datepicker-year {
          color: #dc3545;
        }
      }
    }

    .p-datepicker-calendar {
      td > span.p-highlight {
        background-color: #dc3545;
      }
    }
  }

  // Fix dropdown width issues and add consistent focus styling
  .p-dropdown {
    width: 100% !important;
    height: 39px !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;

    &:not(.p-disabled):hover {
      border-color: #28a745 !important;
    }

    &.p-focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }

    .p-dropdown-label {
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
    }

    .p-dropdown-trigger {
      width: 3rem;
    }

    .p-dropdown-panel {
      width: 100%;
    }
    .p-select-label {
      padding-top: 5px;
      padding-left: 8px;
    }
  }

  // SelectButton styling for location type
  .p-selectbutton {
    .p-button {
      background-color: #fff;
      color: #6c757d;
      border: 1px solid #ced4da;
      transition:
        background-color 0.2s,
        color 0.2s,
        border-color 0.2s;

      &:not(:last-child) {
        border-right: 0;
      }

      &.p-highlight {
        background-color: #dc3545;
        color: #fff;
        border-color: #dc3545;
      }

      &:hover:not(.p-highlight) {
        background-color: #f8f9fa;
      }
    }
  }

  // Calendar styling with consistent green focus effect
  .p-calendar {
    width: 100%;
    height: 39px !important;

    &.p-focus {
      .p-inputtext {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
      }
    }

    .p-inputtext {
      width: 100%;
      height: 39px !important;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;

      &:focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
      }

      &:hover:not(:focus) {
        border-color: #28a745 !important;
      }
    }

    .p-datepicker-trigger {
      background-color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: #bb2d3b;
        border-color: #bb2d3b;
      }
    }
  }

  .p-datepicker {
    .p-datepicker-header {
      .p-datepicker-title {
        .p-datepicker-month,
        .p-datepicker-year {
          color: #dc3545;
        }
      }
    }

    .p-datepicker-calendar {
      td > span.p-highlight {
        background-color: #dc3545;
      }
    }
  }
}
