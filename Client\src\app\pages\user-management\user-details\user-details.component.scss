// User page container
.user-page-container {
  min-height: 100vh;
}

// Navigation header
.user-nav-header {
  padding: 1rem 0;
  margin-bottom: 1.5rem;

  // Using global back button styles
}

// User details container
.user-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 3rem;
  position: relative;

  .content-container {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    max-width: 1080px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
  }

  .content-section {
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }
}

.status-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-label {
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 8px;
}

.status-radio {
  accent-color: #409eff;
  width: 20px;
  height: 20px;
  margin-right: 6px;
}

:host ::ng-deep .p-button-danger {
  height: 45px !important;
  background-color: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;

  &:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
  }
}

.status-active {
  color: #7ed957;
  font-weight: 500;
  margin-right: 18px;
}

.status-inactive {
  color: #bfc3c9;
  font-weight: 500;
}

// Responsive adjustments
@media (max-width: 768px) {
  .user-details-container {
    .content-container {
      padding: 1.5rem;
    }
  }
}
