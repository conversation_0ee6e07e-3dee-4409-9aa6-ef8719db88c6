.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f7fbfd;
  border-radius: 8px;
}

.filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 38px;
}

.filter-btn {
  border-radius: 8px;
  height: 38px;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }

  .actions {
    display: flex;
    gap: 10px;
  }

  // Standardized button styling to match user list component
  :host ::ng-deep .p-button {
    border-radius: 4px;
    padding: 0.75rem 1.25rem !important;
    height: auto !important;
    min-width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  :host ::ng-deep .p-button-outlined.p-button-danger {
    background-color: white !important;
    color: #dc3545 !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: rgba(220, 53, 69, 0.04) !important;
      border-color: #c82333 !important;
      color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }

  :host ::ng-deep .p-button-danger:not(.p-button-outlined) {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: #c82333 !important;
      border-color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }

    &:disabled {
      background-color: #e9a8ae !important;
      border-color: #e9a8ae !important;
      cursor: not-allowed !important;
    }
  }
}

.content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    label {
      font-weight: 500;
      color: #333;
    }

    .password-input-container {
      position: relative;
      display: flex;
      align-items: center;

      input {
        width: 100%;
        padding: 10px 40px 10px 16px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 16px;
        transition: border-color 0.15s ease-in-out;

        &:focus {
          outline: none;
          border-color: #dc3545;
          box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        &.is-invalid {
          border-color: #dc3545;
        }
      }

      .toggle-password {
        position: absolute;
        right: 8px;
        background: none;
        border: none;
        cursor: pointer;
        color: #6c757d;

        &:hover {
          color: #343a40;
        }
      }
    }

    .invalid-feedback {
      color: #dc3545;
      font-size: 14px;
      margin-top: 4px;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}
