.logout-btn {
  cursor: pointer !important;
}

.bell-button {
  position: relative;
  font-size: 1.2rem;
  color: #6c757d; // Initial grey color
  transition: color 0.3s ease;

  &:hover {
    color: #0d6efd; // Blue color on hover
  }

  &.active {
    color: #0d6efd; // Blue color when notifications are open
  }

  i {
    transition: color 0.3s ease;
  }
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 600px;
  max-height: 500px;
  z-index: 1000;
  border-radius: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0; // Prevent header from shrinking
  border-radius: 25px 25px 0 0; // Maintain rounded corners on top
  h5 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
  }
}

// Scrollable content area
.card-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(500px - 60px); // Subtract header height
  border-radius: 0 0 25px 25px; // Maintain rounded corners on bottom

  // Custom scrollbar styling for smooth appearance
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: #a8a8a8;
    }
  }

  // Smooth scrolling behavior
  scroll-behavior: smooth;
}

.notification-list {
  padding: 0;
}

.notification-group {
  margin-bottom: 0;
}

.notification-group-header {
  padding: 6px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;

  h6 {
    color: #666;
    font-weight: 600;
    font-size: 12px;
    margin: 0;
  }
}

.notification-item {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  align-items: flex-start;
  min-height: 50px;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-dot {
  width: 6px;
  height: 6px;
  background-color: #ff0000;
  border-radius: 50%;
  margin-right: 10px;
  margin-top: 4px;
  flex-shrink: 0;
}

.notification-dot-placeholder {
  width: 6px;
  height: 6px;
  margin-right: 10px;
  margin-top: 4px;
  flex-shrink: 0;
}

.notification-content {
  flex-grow: 1;
  padding-right: 35px; // Space for checkbox
  padding-bottom: 20px; // Reduced bottom padding for status badge
  font-size: 13px;
  line-height: 1.3;
}

.event-link,
.organizer-link {
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;
  line-height: 1.2;
  &:hover {
    text-decoration: underline;
  }
}

.notification-time {
  color: #666;
  font-size: 11px;
  margin-top: 2px;
}

// Checkbox positioned at top-right
.notification-checkbox {
  position: absolute;
  top: 8px;
  right: 12px;
  z-index: 10;
}

// Style the HTML checkbox input
.notification-checkbox-input {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 2px solid #dee2e6;
  background: #fff;
  transition: all 0.2s;
  cursor: pointer;
  accent-color: #007bff; // Modern browsers support this for checkbox color

  &:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
  }

  &:checked {
    background: #007bff;
    border-color: #007bff;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

// Status badge positioned at bottom-right
.notification-status {
  position: absolute;
  right: 12px;
  bottom: 8px;
  padding: 0.1rem 0.4rem;
  border-radius: 9999px;
  font-size: 0.65rem;
  font-weight: 500;
  white-space: nowrap;
  height: fit-content;

  &.draft {
    background-color: #f5f5f5; // Light gray background
    color: #757575; // Dark gray text
  }

  &.pending-review {
    background-color: #fff8e1; // Light yellow background
    color: #f59e0b; // Dark yellow text
  }

  &.approved {
    background-color: #e8f5e9; // Light green background (matches Figma)
    color: #2e7d32; // Dark green text (matches Figma)
  }

  &.event-started {
    background-color: #e3f2fd; // Light blue background
    color: #1565c0; // Dark blue text
  }

  &.rejected {
    background-color: #ffebee; // Light red background (matches Figma)
    color: #c62828; // Dark red text (matches Figma)
  }
}

// Badge styles for notification count
.badge {
  font-size: 0.65rem;
  padding: 0.25em 0.5em;
}

// Clear All button styling
.clear-all-btn {
  font-size: 11px;
  padding: 3px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
  }

  i {
    font-size: 10px;
  }
}

// Responsive design for notification panel
@media (max-width: 768px) {
  .notification-panel {
    width: 90vw;
    max-width: 400px;
    right: 5vw;
    max-height: 70vh;

    .card-body {
      max-height: calc(70vh - 60px);
    }
  }
}

@media (max-width: 480px) {
  .notification-panel {
    width: 95vw;
    right: 2.5vw;
    max-height: 80vh;

    .card-body {
      max-height: calc(80vh - 60px);

      // Slightly wider scrollbar on mobile for better touch interaction
      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }

  .notification-item {
    padding: 10px 12px; // Slightly more padding for touch targets
  }
}
