import {
  Component,
  OnInit,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import {
  Event,
  EventType,
  EventLocationType,
  Category,
} from '../../../Core/Models/events';
import { AuthService } from '../../../Core/Services/auth.service';
import { EventsService } from '../../../Core/Services/events.service';
import { UserDetailsService } from '../../../Core/Services/UserDetails.service';
import { DateUtilsService } from '../../../Core/Services/date-utils.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-event-list',
  standalone: false,
  templateUrl: './event-list.component.html',
  styleUrl: './event-list.component.scss',
  providers: [MessageService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventListComponent implements OnInit {
  Math = Math;
  EventLocationType = EventLocationType; // Make enum available to template
  events: Event[] = [];
  filteredEvents: Event[] = [];
  error: string | null = null;
  isLoading: boolean = false;
  router = inject(Router);
  showFilters: boolean = false;
  filterForm: FormGroup;
  advancedFilterForm: FormGroup;

  // Progressive disclosure filter properties
  showAdvancedFilters: boolean = false;
  activeFilterChips: { key: string; label: string }[] = [];
  activeFiltersCount: number = 0;

  activeTab: 'all' | 'pending' = 'all';
  pendingReviewCount: number = 0;

  // Pagination
  currentPage: number = 1;
  pageSize: number = 5;
  totalItems: number = 0;
  totalPages: number = 0;

  // Sorting
  currentSortField?: string;
  currentSortOrder: 'asc' | 'desc' = 'asc';

  // Filter options
  eventStatusOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
    { name: 'Draft', value: 'Draft' },
    { name: 'Submitted', value: 'Submitted' },
    { name: 'Approved', value: 'Approved' },
    { name: 'Rejected', value: 'Rejected' },
    { name: 'Cancelled', value: 'Cancelled' },
  ];

  approvalStatusOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
    { name: 'Approved', value: 'true' },
    { name: 'Not Approved', value: 'false' },
  ];

  typeOptions: { name: string; value: string | number }[] = [
    { name: 'All', value: '' },
    { name: 'Appearance Or Signing', value: EventType.AppearanceOrSigning },
    { name: 'Attraction', value: EventType.Attraction },
    { name: 'Camp Trip Or Retreat', value: EventType.CampTripOrRetreat },
    {
      name: 'Class Training Or Workshop',
      value: EventType.ClassTrainingOrWorkshop,
    },
    { name: 'Concert Or Performance', value: EventType.ConcertOrPerformance },
    { name: 'Conference', value: EventType.Conference },
    { name: 'Convention', value: EventType.Convention },
    { name: 'Dinner Or Gala', value: EventType.DinnerOrGala },
    { name: 'Festival Or Fair', value: EventType.FestivalOrFair },
    { name: 'Games Or Competition', value: EventType.GamesOrCompetition },
    {
      name: 'Meeting Or Networking Event',
      value: EventType.MeetingOrNetworkingEvent,
    },
    { name: 'Other', value: EventType.Other },
    {
      name: 'Party Or Social Gathering',
      value: EventType.PartyOrSocialGathering,
    },
    { name: 'Rally', value: EventType.Rally },
    { name: 'Screening', value: EventType.Screening },
    { name: 'Seminar Or Talk', value: EventType.SeminarOrTalk },
    { name: 'Tour', value: EventType.Tour },
    { name: 'Tournament', value: EventType.Tournament },
    {
      name: 'Trade Show Consumer Show Or Expo',
      value: EventType.TradeShowConsumerShowOrExpo,
    },
  ];

  categoryOptions: { name: string; value: string | number }[] = [
    { name: 'All', value: '' },
    { name: 'Careers And Employment', value: Category.CareersAndEmployment },
    { name: 'Community Resources', value: Category.CommunityResources },
    { name: 'Early Childhood', value: Category.EarlyChildhood },
    { name: 'Health And Wellness', value: Category.HealthWellness },
    { name: 'Maternal Health Care', value: Category.MaternalHealthCare },
    { name: 'Rental Housing', value: Category.RentalHousing },
  ];

  organizerOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
  ];

  constructor(
    private readonly eventsService: EventsService,
    private readonly userService: UserDetailsService,
    private readonly messageService: MessageService,
    private fb: FormBuilder,
    public authService: AuthService,
    private dateUtils: DateUtilsService,
    private cdr: ChangeDetectorRef,
  ) {
    // Main filter form (includes all filter controls)
    this.filterForm = this.fb.group({
      searchTerm: [''],
      eventStartDateFrom: [null],
      eventStatus: [''],
      organizer: [''],
      approvalStatus: [''],
      type: [''],
      category: [''],
      submittedOn: [null],
      eventReviewedOn: [null],
    });

    // Advanced filter form (modal) - keeping for backward compatibility
    this.advancedFilterForm = this.fb.group({
      eventStartDateFrom: [null],
      eventStartDateTo: [null],
      submittedOn: [null],
      eventReviewedOn: [null],
      type: [''],
      category: [''],
      approvalStatus: [''],
      organizer: [''],
    });

    // Debounce filter changes for all filters
    this.filterForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.currentPage = 1;
      this.updateActiveFilters();
      this.loadEvents();
      this.cdr.markForCheck();
    });
  }

  ngOnInit() {
    this.updateActiveFilters();
    this.loadEvents();
    // Don't load organizers here - we'll load them only when needed
  }

  loadOrganizers(): void {
    // Get unique organizer and submitter names from the current events
    const uniqueNames = new Set<string>();

    this.events.forEach((event) => {
      // Prioritize submitter name (event creator) over organizer name
      if (event.submitterName) {
        uniqueNames.add(event.submitterName);
      } else if (event.organizerName) {
        uniqueNames.add(event.organizerName);
      }
    });

    // Convert to dropdown options and sort alphabetically
    const nameOptions = Array.from(uniqueNames)
      .filter((name) => name && name.trim() !== '') // Filter out empty names
      .sort()
      .map((name) => ({
        name: name,
        value: name,
      }));

    this.organizerOptions = [{ name: 'All', value: '' }, ...nameOptions];

    // If no events are loaded yet or no names found, fallback to loading users by role
    if (this.events.length === 0 || nameOptions.length === 0) {
      this.userService.getUsersByRole('Event Organizer').subscribe({
        next: (users) => {
          const userOptions = users
            .filter((user) => user.fullName && user.fullName.trim() !== '')
            .map((user) => ({
              name: user.fullName,
              value: user.fullName, // Use name instead of ID for consistent filtering
            }))
            .sort((a, b) => a.name.localeCompare(b.name));

          // Merge with existing options and remove duplicates
          const existingNames = new Set(
            this.organizerOptions.map((opt) => opt.value),
          );
          const newOptions = userOptions.filter(
            (opt) => !existingNames.has(opt.value),
          );

          this.organizerOptions = [
            { name: 'All', value: '' },
            ...nameOptions,
            ...newOptions,
          ];
        },
        error: (err) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load organizers. ' + err.message,
          });
        },
      });
    }
  }

  loadEvents(): void {
    this.error = null;
    this.isLoading = true;

    // Use only the main filter form (no more merging with advanced filters)
    const formValues = this.filterForm.value;
    const filters = { ...formValues }; // Create a mutable copy to avoid modifying the form's state directly

    // Note: Date filtering is now handled consistently in the service layer
    // All date filters (eventStartDateFrom, submittedOn, eventReviewedOn) use the same
    // toISOString() approach in the events service for consistent timezone handling

    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortField: this.currentSortField,
      sortOrder: this.currentSortOrder,
      filters: filters,
    };

    this.eventsService
      .getEvents(params)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        }),
      )
      .subscribe({
        next: (response) => {
          try {
            if (response) {
              this.events = response.items || [];
              this.totalItems = response.totalItems || 0;
              this.totalPages = response.totalPages || 0;

              this.processEvents();

              // Update organizer options after events are loaded
              if (this.showFilters || this.showAdvancedFilters) {
                this.loadOrganizers();
              }

              // Trigger change detection for OnPush strategy
              this.cdr.markForCheck();
            } else {
              throw new Error('Invalid response format');
            }
          } catch (error) {
            this.error = 'Failed to process event data';
            this.events = [];
            this.totalItems = 0;
            this.totalPages = 0;
            this.cdr.markForCheck();
          }
        },
        error: (err) => {
          this.error = err.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: err.message || 'Failed to load events. Please try again.',
          });
          this.cdr.markForCheck();
        },
      });
  }

  processEvents(): void {
    // IMPORTANT: Don't override the filtered results from backend!
    // The backend already handles all filtering based on the filter parameters.
    // We only need to apply client-side logic for UI-specific requirements.

    // Count pending review events for the badge
    // Filter out events that are within 30 minutes of starting for pending count only
    const eventsNotWithin30Min = this.events.filter((event) => {
      if (!event.eventStarts) return true;

      // Convert UTC event start time to IST before comparing
      const eventStartTime = this.dateUtils
        .convertUtcToIst(event.eventStarts)
        .getTime();
      const thirtyMinutesBefore = eventStartTime - 30 * 60 * 1000; // 30 minutes in milliseconds
      const now = new Date().getTime();

      return now < thirtyMinutesBefore;
    });

    // Count pending review events (excluding those within 30 minutes of starting)
    this.pendingReviewCount = eventsNotWithin30Min.filter(
      (event) => event.statusName === 'Submitted',
    ).length;

    // CRITICAL FIX: Use the events from backend as-is (they are already filtered)
    // The backend handles all filtering, we just display the results
    this.filteredEvents = [...this.events];

    // Trigger change detection for OnPush strategy
    this.cdr.markForCheck();
  }

  getFullImagePath(relativePath: string): string {
    if (!relativePath) {
      return 'assets/images/placeholder.jpg';
    }

    // If it's already a full URL (from S3), return it as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Check if the path already contains /api/Files
    if (relativePath.includes('/api/Files/')) {
      return `${environment.apiUrl}${relativePath}`;
    }

    // Otherwise, use the Files controller to get the full path
    return `${environment.apiUrl}/api/Files/${relativePath.replace(/^\/+/, '')}`;
  }

  setActiveTab(tab: 'all' | 'pending'): void {
    this.activeTab = tab;

    // Reset to first page when switching tabs
    this.currentPage = 1;

    // Apply tab-specific filtering by updating the form and reloading events
    if (tab === 'pending') {
      // For pending tab, filter by 'Submitted' status
      this.filterForm.patchValue(
        { eventStatus: 'Submitted' },
        { emitEvent: false },
      );
    } else {
      // For all tab, clear the status filter if it was set to 'Submitted' by pending tab
      const currentStatus = this.filterForm.get('eventStatus')?.value;
      if (currentStatus === 'Submitted') {
        this.filterForm.patchValue({ eventStatus: '' }, { emitEvent: false });
      }
    }

    // Reload events with the new tab filter
    this.loadEvents();

    // Additional change detection trigger for tab switching
    this.cdr.markForCheck();
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;

    // Load organizers only when the filter is opened and we haven't loaded them yet
    if (this.showFilters && this.organizerOptions.length <= 1) {
      this.loadOrganizers();
    }

    // Trigger change detection for OnPush strategy
    this.cdr.markForCheck();
  }

  // Progressive disclosure filter methods
  openAdvancedFilters(): void {
    this.showAdvancedFilters = true;

    // Always load organizers when opening advanced filters to ensure fresh data
    this.loadOrganizers();

    this.cdr.markForCheck();
  }

  applyAdvancedFilters(): void {
    this.showAdvancedFilters = false;
    this.currentPage = 1;
    this.updateActiveFilters();
    this.loadEvents();
    this.cdr.markForCheck();
  }

  cancelAdvancedFilters(): void {
    this.showAdvancedFilters = false;
    this.cdr.markForCheck();
  }

  clearAdvancedFilters(): void {
    this.advancedFilterForm.reset();
    this.cdr.markForCheck();
  }

  clearAllFilters(): void {
    // Reset the main filter form with proper default values
    this.filterForm.patchValue({
      searchTerm: '',
      eventStartDateFrom: null,
      eventStatus: '',
      organizer: '',
      approvalStatus: '',
      type: '',
      category: '',
      submittedOn: null,
      eventReviewedOn: null,
    });

    // Reset advanced filter form for backward compatibility
    this.advancedFilterForm.patchValue({
      eventStartDateFrom: null,
      eventStartDateTo: null,
      submittedOn: null,
      eventReviewedOn: null,
      type: '',
      category: '',
      approvalStatus: '',
      organizer: '',
    });

    this.activeFilterChips = [];
    this.activeFiltersCount = 0;
    this.currentPage = 1;
    this.updateActiveFilters();
    this.loadEvents();
    this.cdr.markForCheck();
  }

  removeFilter(filterKey: string): void {
    // Reset specific filter based on key - prioritize main filter form
    if (this.filterForm.get(filterKey)) {
      if (filterKey.includes('Date') || filterKey.includes('On')) {
        this.filterForm.get(filterKey)?.setValue(null);
      } else {
        this.filterForm.get(filterKey)?.setValue('');
      }
    } else if (this.advancedFilterForm.get(filterKey)) {
      if (filterKey.includes('Date') || filterKey.includes('On')) {
        this.advancedFilterForm.get(filterKey)?.setValue(null);
      } else {
        this.advancedFilterForm.get(filterKey)?.setValue('');
      }
    }

    this.updateActiveFilters();
    this.currentPage = 1;
    this.loadEvents();
    this.cdr.markForCheck();
  }

  updateActiveFilters(): void {
    this.activeFilterChips = [];

    // Check main filter form (includes all filter controls)
    const filters = this.filterForm.value;

    // Search term filter
    if (filters.searchTerm) {
      this.activeFilterChips.push({
        key: 'searchTerm',
        label: `Search: ${filters.searchTerm}`,
      });
    }

    // Event start date filter
    if (filters.eventStartDateFrom) {
      this.activeFilterChips.push({
        key: 'eventStartDateFrom',
        label: `Start Date: ${new Date(filters.eventStartDateFrom).toLocaleDateString()}`,
      });
    }

    // Event status filter
    if (filters.eventStatus) {
      const statusOption = this.eventStatusOptions.find(
        (opt) => opt.value === filters.eventStatus,
      );
      this.activeFilterChips.push({
        key: 'eventStatus',
        label: `Status: ${statusOption?.name || filters.eventStatus}`,
      });
    }

    // Organizer filter
    if (filters.organizer) {
      const organizerOption = this.organizerOptions.find(
        (opt) => opt.value === filters.organizer,
      );
      this.activeFilterChips.push({
        key: 'organizer',
        label: `Organizer: ${organizerOption?.name || filters.organizer}`,
      });
    }

    // Approval status filter
    if (filters.approvalStatus) {
      const approvalOption = this.approvalStatusOptions.find(
        (opt) => opt.value === filters.approvalStatus,
      );
      this.activeFilterChips.push({
        key: 'approvalStatus',
        label: `Approval: ${approvalOption?.name || filters.approvalStatus}`,
      });
    }

    // Event type filter
    if (filters.type) {
      const typeOption = this.typeOptions.find(
        (opt) => opt.value == filters.type, // Use == for loose comparison to handle string/number
      );
      this.activeFilterChips.push({
        key: 'type',
        label: `Type: ${typeOption?.name || filters.type}`,
      });
    }

    // Category filter
    if (filters.category) {
      const categoryOption = this.categoryOptions.find(
        (opt) => opt.value == filters.category, // Use == for loose comparison to handle string/number
      );
      this.activeFilterChips.push({
        key: 'category',
        label: `Category: ${categoryOption?.name || filters.category}`,
      });
    }

    // Submitted on date filter
    if (filters.submittedOn) {
      this.activeFilterChips.push({
        key: 'submittedOn',
        label: `Submitted: ${new Date(filters.submittedOn).toLocaleDateString()}`,
      });
    }

    // Event reviewed on date filter
    if (filters.eventReviewedOn) {
      this.activeFilterChips.push({
        key: 'eventReviewedOn',
        label: `Reviewed: ${new Date(filters.eventReviewedOn).toLocaleDateString()}`,
      });
    }

    this.activeFiltersCount = this.activeFilterChips.length;
  }

  clearFilters(): void {
    // Reset the main filter form with proper default values
    this.filterForm.patchValue({
      searchTerm: '',
      eventStartDateFrom: null,
      eventStatus: '',
      organizer: '',
      approvalStatus: '',
      type: '',
      category: '',
      submittedOn: null,
      eventReviewedOn: null,
    });

    // Reset pagination to first page
    this.currentPage = 1;

    // Clear any active filter tracking
    this.activeFilterChips = [];
    this.activeFiltersCount = 0;

    // Reload events with cleared filters
    this.loadEvents();

    // Show success message
    this.messageService.add({
      severity: 'success',
      summary: 'Filters Cleared',
      detail: 'All filters have been reset successfully.',
      life: 3000,
    });

    // Trigger change detection
    this.cdr.markForCheck();
  }

  sortEvents(field: keyof Event): void {
    if (this.currentSortField === field) {
      this.currentSortOrder = this.currentSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.currentSortField = field;
      this.currentSortOrder = 'asc';
    }
    this.loadEvents();
  }

  viewEventDetails(eventId: number): void {
    this.router.navigate(['/events', eventId]);
  }

  addEvent(): void {
    this.router.navigate(['/events/new']);
  }

  editEvent(eventId: number): void {
    this.router.navigate(['/events', eventId, 'edit']);
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadEvents();
    }
  }

  get pages(): number[] {
    if (this.totalPages <= 5) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 3) {
      return [1, 2, 3, 4, 5];
    }

    if (this.currentPage >= this.totalPages - 2) {
      return Array.from({ length: 5 }, (_, i) => this.totalPages - 4 + i);
    }

    return Array.from({ length: 5 }, (_, i) => this.currentPage - 2 + i);
  }

  // Check if the event has already started
  hasEventStarted(event: Event): boolean {
    if (!event || !event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    return now >= eventStartTime;
  }

  // TrackBy function for ngFor performance optimization
  trackByEventId(_index: number, event: Event): number {
    return event.id;
  }

  // Helper methods to get selected text for tooltips
  getSelectedEventStatusText(): string {
    const value = this.filterForm.get('eventStatus')?.value;
    if (!value) return 'All';
    const option = this.eventStatusOptions.find((opt) => opt.value === value);
    return option?.name || 'All';
  }

  getSelectedOrganizerText(): string {
    const value = this.filterForm.get('organizer')?.value;
    if (!value) return 'All';
    const option = this.organizerOptions.find((opt) => opt.value === value);
    return option?.name || 'All';
  }

  getSelectedApprovalStatusText(): string {
    const value = this.filterForm.get('approvalStatus')?.value;
    if (!value) return 'All';
    const option = this.approvalStatusOptions.find(
      (opt) => opt.value === value,
    );
    return option?.name || 'All';
  }

  getSelectedTypeText(): string {
    const value = this.filterForm.get('type')?.value;
    if (!value) return 'All';
    const option = this.typeOptions.find((opt) => opt.value === value);
    return option?.name || 'All';
  }

  getSelectedCategoryText(): string {
    const value = this.filterForm.get('category')?.value;
    if (!value) return 'All';
    const option = this.categoryOptions.find((opt) => opt.value === value);
    return option?.name || 'All';
  }
  formatEventType(type: string): string {
    switch (type) {
      case 'AppearanceOrSigning':
        return 'Appearance Or Signing';
      case 'Attraction':
        return 'Attraction';
      case 'CampTripOrRetreat':
        return 'Camp Trip Or Retreat';
      case 'ClassTrainingOrWorkshop':
        return 'Class Training Or Workshop';
      case 'ConcertOrPerformance':
        return 'Concert Or Performance';
      case 'Conference':
        return 'Conference';
      case 'Convention':
        return 'Convention';
      case 'DinnerOrGala':
        return 'Dinner Or Gala';
      case 'FestivalOrFair':
        return 'Festival Or Fair';
      case 'GamesOrCompetition':
        return 'Games Or Competition';
      case 'MeetingOrNetworkingEvent':
        return 'Meeting Or Networking Event';
      case 'Other':
        return 'Other';
      case 'PartyOrSocialGathering':
        return 'Party Or Social Gathering';
      case 'Rally':
        return 'Rally';
      case 'Screening':
        return 'Screening';
      case 'SeminarOrTalk':
        return 'Seminar Or Talk';
      case 'Tour':
        return 'Tour';
      case 'Tournament':
        return 'Tournament';
      case 'TradeShowConsumerShowOrExpo':
        return 'Trade Show Consumer Show Or Expo';
      default:
        return type;
    }
  }

  formatEventCategory(category: string): string {
    switch (category) {
      case 'CareersAndEmployment':
        return 'Careers And Employment';
      case 'CommunityResources':
        return 'Community Resources';
      case 'EarlyChildhood':
        return 'Early Childhood';
      case 'HealthWellness':
        return 'Health And Wellness';
      case 'MaternalHealthCare':
        return 'Maternal Health Care';
      case 'RentalHousing':
        return 'Rental Housing';
      default:
        return category;
    }
  }
}
