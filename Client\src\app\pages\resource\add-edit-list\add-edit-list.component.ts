import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { finalize, catchError } from 'rxjs/operators';
import { ResourcesService } from '../../../Core/Services/resources.service';
import {
  Resource,
  ResourceType,
  ApiResponse,
} from '../../../Core/Models/resources';
import { HttpErrorResponse } from '@angular/common/http';
import { EMPTY, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-add-edit-list',
  standalone: false,
  templateUrl: './add-edit-list.component.html',
  styleUrl: './add-edit-list.component.scss',
  providers: [MessageService],
})
export class AddEditListComponent implements OnInit {
  resourceForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  resourceId: number | null = null;
  isEditMode = false;
  resourceTypes = ResourceType;
  formSubmitted = false;
  socialMediaErrors: string[] = [];
  private apiUrl = environment.apiUrl;

  getFullImagePath(relativePath: string): string {
    if (!relativePath) return 'assets/images/placeholder.jpg';
    if (relativePath.startsWith('http')) return relativePath;
    if (
      relativePath.includes('ResourceImages') ||
      relativePath.includes('ResourceLogos')
    ) {
      return `${this.apiUrl}/api/Files${relativePath.replace(/^\/+/, '/')}`;
    }
    return `${this.apiUrl}${relativePath.startsWith('/') ? '' : '/'}${relativePath}`;
  }

  resourceImage: File | null = null;
  resourceLogo: File | null = null;
  resourceImagePreview: string | null = null;
  resourceLogoPreview: string | null = null;

  resourceCategories: string[] = [
    'Education',
    'Healthcare',
    'Housing',
    'Employment',
    'Food Assistance',
    'Mental Health',
    'Community Services',
    'Youth Programs',
    'Senior Services',
    'Legal Aid',
  ];

  resourceCategoryOptions: { label: string; value: string }[] = [
    { label: 'Education', value: 'Education' },
    { label: 'Healthcare', value: 'Healthcare' },
    { label: 'Housing', value: 'Housing' },
    { label: 'Employment', value: 'Employment' },
    { label: 'Food Assistance', value: 'Food Assistance' },
    { label: 'Mental Health', value: 'Mental Health' },
    { label: 'Community Services', value: 'Community Services' },
    { label: 'Youth Programs', value: 'Youth Programs' },
    { label: 'Senior Services', value: 'Senior Services' },
    { label: 'Legal Aid', value: 'Legal Aid' },
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private resourceService: ResourcesService,
    private messageService: MessageService,
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.socialMediaErrors = [];
    this.setupResourceMode();
    this.setupFormListeners();
  }

  setupResourceMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.resourceId = Number(id);
      this.isEditMode = true;
      this.loadResourceData(Number(id));
    }
  }

  setupFormListeners(): void {
    // Clear duplicate title error when the user changes the title
    this.resourceForm
      .get('basicInfo.organizationTitle')
      ?.valueChanges.subscribe(() => {
        this.clearDuplicateTitleError();
      });

    // Clear social media errors when any social media field changes
    ['facebook', 'instagram', 'twitter', 'linkedIn'].forEach((field) => {
      this.resourceForm
        .get(`socialMedia.${field}`)
        ?.valueChanges.subscribe(() => {
          if (this.socialMediaErrors.length > 0) {
            this.socialMediaErrors = [];
          }
        });
    });
  }

  clearDuplicateTitleError(): void {
    const titleControl = this.resourceForm.get('basicInfo.organizationTitle');
    if (titleControl?.hasError('duplicateTitle')) {
      const currentErrors = titleControl.errors;
      if (currentErrors) {
        const { duplicateTitle, ...otherErrors } = currentErrors;
        titleControl.setErrors(
          Object.keys(otherErrors).length ? otherErrors : null,
        );
      }
    }
  }

  initForm(): void {
    this.resourceForm = this.fb.group({
      basicInfo: this.fb.group({
        organizationTitle: ['', [Validators.required]],
        subTitle: [''],
        resourceCategory: ['', [Validators.required]],
        address: ['', [Validators.required]],
        city: ['', [Validators.required]],
        state: ['', [Validators.required]],
        zipCode: ['', [Validators.required]],
        shortDescription: ['', [Validators.required]],
        longDescription: ['', [Validators.required]],
        type: [ResourceType.ExternalPartner, [Validators.required]],
      }),
      services: this.fb.array([this.fb.control('', Validators.required)]),
      contactDetails: this.fb.group({
        contactName: ['', [Validators.required]],
        contactNo: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        website: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+\\/?.*',
            ),
          ],
        ],
      }),
      socialMedia: this.fb.group({
        facebook: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '(https?:\\/\\/)?(www\\.)?facebook\\.com\\/[a-zA-Z0-9\\._-]+\\/?.*',
            ),
          ],
        ],
        instagram: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '(https?:\\/\\/)?(www\\.)?instagram\\.com\\/[A-Za-z0-9_.]+\\/?.*',
            ),
          ],
        ],
        twitter: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '(https?:\\/\\/)?(www\\.)?(twitter\\.com|x\\.com)\\/[A-Za-z0-9_]+\\/?.*',
            ),
          ],
        ],
        linkedIn: [
          '',
          [
            Validators.required,
            Validators.pattern(
              '(https?:\\/\\/)?(www\\.)?linkedin\\.com\\/(in|company)\\/[A-Za-z0-9_-]+\\/?.*',
            ),
          ],
        ],
      }),
    });
  }

  loadResourceData(id: number): void {
    this.isLoading = true;
    this.resourceService
      .GetResourceById(id)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (response: ApiResponse<Resource>) => {
          let resourceData: Resource;

          if (response && !response.data) {
            resourceData = response as any;
          } else {
            resourceData = response.data!;
          }

          if (resourceData) {
            this.processResourceData(resourceData);
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Resource not found',
            });
          }
        },
        error: (error: HttpErrorResponse) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.message || 'Failed to load resource data',
          });
        },
      });
  }

  processResourceData(resourceData: Resource): void {
    // Ensure resource type is properly set
    if (resourceData.type === undefined && resourceData.resourceTypeName) {
      resourceData.type = this.getResourceTypeFromName(
        resourceData.resourceTypeName,
      );
    }

    this.patchFormWithResourceData(resourceData);
    this.setResourcePreviews(resourceData);
  }

  getResourceTypeFromName(typeName: string): ResourceType {
    switch (typeName) {
      case 'External Partner':
        return ResourceType.ExternalPartner;
      case 'South Ward Promise Neighbourhood':
        return ResourceType.SouthWardPromiseNeighbourhood;
      case 'SWPN Partner':
        return ResourceType.SWPNPartner;
      default:
        return ResourceType.ExternalPartner;
    }
  }

  setResourcePreviews(resourceData: Resource): void {
    if (resourceData.resourceImagePath || resourceData.resourceImageUrl) {
      this.resourceImagePreview =
        resourceData.resourceImagePath || resourceData.resourceImageUrl;
    }

    if (resourceData.resourceLogoPath || resourceData.resourceLogoUrl) {
      this.resourceLogoPreview =
        resourceData.resourceLogoPath || resourceData.resourceLogoUrl;
    }
  }

  patchFormWithResourceData(resource: Resource): void {
    this.patchServicesArray(resource);
    this.patchBasicInfo(resource);
    this.patchContactDetails(resource);
    this.patchSocialMedia(resource);
  }

  patchServicesArray(resource: Resource): void {
    this.servicesArray.clear();

    if (resource.services && resource.services.length > 0) {
      resource.services.forEach((service) => {
        this.servicesArray.push(this.fb.control(service, Validators.required));
      });
    } else {
      this.addService();
    }
  }

  patchBasicInfo(resource: Resource): void {
    // Ensure we have a valid resource type
    const resourceTypeValue = resource.type ?? ResourceType.ExternalPartner;

    this.resourceForm.get('basicInfo')?.patchValue({
      organizationTitle: resource.organizationTitle,
      subTitle: resource.subTitle,
      resourceCategory: resource.resourceCategory,
      address: resource.address,
      city: resource.city,
      state: resource.state,
      zipCode: resource.zipCode,
      shortDescription: resource.shortDescription,
      longDescription: resource.longDescription,
      type: resourceTypeValue,
    });
  }

  patchContactDetails(resource: Resource): void {
    if (!resource.contactDetails) return;

    const contactDetailsToSet = {
      contactName:
        resource.contactDetails.contactName || resource.organizationTitle || '',
      contactNo: resource.contactDetails.contactNo || '',
      email: resource.contactDetails.email || '',
      website: resource.contactDetails.website || '',
    };

    this.resourceForm.get('contactDetails')?.patchValue(contactDetailsToSet);
  }

  patchSocialMedia(resource: Resource): void {
    if (!resource.socialMedia) return;

    this.resourceForm.get('socialMedia')?.patchValue({
      facebook: resource.socialMedia.facebook || '',
      instagram: resource.socialMedia.instagram || '',
      twitter: resource.socialMedia.twitter || '',
      linkedIn: resource.socialMedia.linkedIn || '',
    });
  }

  get servicesArray(): FormArray {
    return this.resourceForm.get('services') as FormArray;
  }

  addService(): void {
    this.servicesArray.push(this.fb.control('', Validators.required));
  }

  updateServiceValue(index: number, value: string): void {
    if (index >= 0 && index < this.servicesArray.length) {
      this.servicesArray.at(index).setValue(value);
    }
  }

  removeService(index: number): void {
    if (this.servicesArray.length > 1) {
      this.servicesArray.removeAt(index);
    } else {
      this.messageService.add({
        severity: 'info',
        summary: 'Info',
        detail: 'At least one service is required',
      });
    }
  }

  validateServices(): boolean {
    if (!this.servicesArray || this.servicesArray.length === 0) {
      return false;
    }

    const serviceValues = this.servicesArray.controls.map((c) => c.value);
    const hasNonEmptyService = serviceValues.some(
      (value) => value && typeof value === 'string' && value.trim().length > 0,
    );

    return hasNonEmptyService;
  }

  // Helper method to check if form is ready for submission
  isFormReadyForSubmission(): boolean {
    const basicInfoValid = this.resourceForm.get('basicInfo')?.valid;
    const contactDetailsValid = this.resourceForm.get('contactDetails')?.valid;
    const socialMediaValid = this.resourceForm.get('socialMedia')?.valid;
    const servicesValid = this.validateServices();
    const filesValid =
      this.isEditMode || (this.resourceImage && this.resourceLogo);

    return !!(
      basicInfoValid &&
      contactDetailsValid &&
      socialMediaValid &&
      servicesValid &&
      filesValid
    );
  }

  onResourceImageSelected(event: Event): void {
    this.handleFileSelection(event, 'image', 5);
  }

  onResourceLogoSelected(event: Event): void {
    this.handleFileSelection(event, 'logo', 2);
  }

  handleFileSelection(
    event: Event,
    fileType: 'image' | 'logo',
    maxSizeMB: number,
  ): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    const maxSizeInBytes = maxSizeMB * 1024 * 1024;

    if (file.size > maxSizeInBytes) {
      input.value = '';

      if (fileType === 'image') {
        this.resourceImage = null;
        this.resourceImagePreview = null;
      } else {
        this.resourceLogo = null;
        this.resourceLogoPreview = null;
      }

      this.messageService.add({
        severity: 'error',
        summary: 'File Size Error',
        detail: `Resource ${fileType === 'image' ? 'Image' : 'Logo'} file size exceeds the maximum allowed size of ${maxSizeMB} MB`,
      });

      return;
    }

    if (fileType === 'image') {
      this.resourceImage = file;
      this.createFilePreview(
        file,
        (preview) => (this.resourceImagePreview = preview),
      );
    } else {
      this.resourceLogo = file;
      this.createFilePreview(
        file,
        (preview) => (this.resourceLogoPreview = preview),
      );
    }
  }

  createFilePreview(file: File, callback: (preview: string) => void): void {
    const reader = new FileReader();
    reader.onload = () => callback(reader.result as string);
    reader.readAsDataURL(file);
  }

  cancel(): void {
    this.socialMediaErrors = [];
    this.router.navigate(['/resources']);
  }

  onTitleChange(): void {
    this.clearDuplicateTitleError();
  }

  handleDuplicateTitleError(): void {
    this.resourceForm
      .get('basicInfo.organizationTitle')
      ?.setErrors({ duplicateTitle: true });
    window.scrollTo({ top: 0, behavior: 'smooth' });

    this.messageService.add({
      severity: 'error',
      summary: 'Duplicate Title',
      detail:
        'A resource with this title already exists. Please use a different title.',
      life: 7000,
    });
  }

  onSubmit(): void {
    this.formSubmitted = true;

    if (!this.validateForm()) return;

    this.isSubmitting = true;
    this.showUploadingMessage();

    const formData = this.prepareFormData();
    const request$ = this.isEditMode
      ? this.resourceService.UpdateResource(formData)
      : this.resourceService.AddResource(formData);

    request$
      .pipe(
        finalize(() => {
          this.isSubmitting = false;
          this.messageService.clear('uploadProgress');
        }),
        catchError((err: HttpErrorResponse) => {
          if (this.isDuplicateTitleError(err)) {
            this.handleDuplicateTitleError();
            return EMPTY;
          }
          return throwError(() => err);
        }),
      )
      .subscribe({
        next: this.handleSuccessResponse.bind(this),
        error: this.handleErrorResponse.bind(this),
      });
  }

  validateForm(): boolean {
    // Check for duplicate title error first
    if (
      this.resourceForm
        .get('basicInfo.organizationTitle')
        ?.hasError('duplicateTitle')
    ) {
      this.handleDuplicateTitleError();
      return false;
    }

    // Validate services first (before checking form validity)
    if (!this.validateServices()) {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'At least one service is required',
      });
      return false;
    }

    // Check form validity
    if (this.resourceForm.invalid) {
      this.markFormGroupTouched(this.resourceForm);

      // Get specific validation errors
      const errors = this.getFormValidationErrors();

      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
      });
      return false;
    }

    // Validate website URL if provided
    if (!this.validateWebsiteUrl()) {
      return false;
    }

    // Validate social media URLs
    if (!this.validateSocialMediaUrls()) {
      return false;
    }

    // Validate required files for new resources
    if (!this.isEditMode) {
      if (!this.resourceImage) {
        this.messageService.add({
          severity: 'error',
          summary: 'Validation Error',
          detail: 'Resource Image is required',
        });
        return false;
      }

      if (!this.resourceLogo) {
        this.messageService.add({
          severity: 'error',
          summary: 'Validation Error',
          detail: 'Resource Logo is required',
        });
        return false;
      }
    }

    // Validate file sizes
    if (!this.validateFileSize(this.resourceImage, 5, 'Image')) {
      return false;
    }
    if (!this.validateFileSize(this.resourceLogo, 2, 'Logo')) {
      return false;
    }

    return true;
  }

  validateFileSize(
    file: File | null,
    maxSizeMB: number,
    fileType: string,
  ): boolean {
    if (!file) return true;

    const maxSizeInBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      this.messageService.add({
        severity: 'error',
        summary: 'File Size Error',
        detail: `Resource ${fileType} file size exceeds the maximum allowed size of ${maxSizeMB} MB`,
      });
      return false;
    }
    return true;
  }

  showUploadingMessage(): void {
    this.messageService.add({
      severity: 'info',
      summary: 'Processing',
      detail: 'Uploading resource data and files...',
      sticky: true,
      key: 'uploadProgress',
    });
  }

  isDuplicateTitleError(error: HttpErrorResponse): boolean {
    const duplicateTitleMessage = 'Resource with this title already exists';

    // Check all possible locations for the error message
    if (error.error === duplicateTitleMessage) return true;

    if (typeof error.error === 'object' && error.error !== null) {
      if (
        error.error.message === duplicateTitleMessage ||
        error.error.Message === duplicateTitleMessage
      ) {
        return true;
      }
    }

    if (error.message === duplicateTitleMessage) return true;

    // Check for partial matches
    if (
      (typeof error.error === 'string' &&
        error.error.includes(duplicateTitleMessage)) ||
      (error.message && error.message.includes(duplicateTitleMessage)) ||
      (error.statusText && error.statusText.includes(duplicateTitleMessage))
    ) {
      return true;
    }

    // Check for keywords in 400 errors
    if (error.status === 400) {
      let errorText = '';
      if (typeof error.error === 'string') {
        errorText = error.error;
      } else if (typeof error.error === 'object' && error.error !== null) {
        errorText = JSON.stringify(error.error);
      }

      if (
        errorText.toLowerCase().includes('duplicate') ||
        errorText.toLowerCase().includes('already exists') ||
        errorText.toLowerCase().includes('title')
      ) {
        return true;
      }
    }

    return false;
  }

  handleSuccessResponse(response: any): void {
    if (response.isSuccess) {
      const successDetail = this.isEditMode
        ? 'Resource updated successfully'
        : 'Resource created successfully';

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: successDetail,
        life: 5000,
      });

      setTimeout(() => {
        this.router.navigate(['/resources']);
      }, 1500);
    } else {
      const errorMessage = response.message || 'Operation failed';

      if (errorMessage === 'Resource with this title already exists') {
        this.handleDuplicateTitleError();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: errorMessage,
        });
      }
    }
  }

  handleErrorResponse(error: HttpErrorResponse): void {
    // Check for duplicate title error
    if (this.isDuplicateTitleError(error)) {
      this.handleDuplicateTitleError();
      return;
    }

    // Handle validation errors
    if (error.error && error.error.errors) {
      this.handleValidationErrors(error.error.errors);
    } else {
      // Generic error message
      const errorMessage = error.message || 'Failed to save resource';
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: errorMessage,
      });
    }
  }

  handleValidationErrors(validationErrors: any): void {
    // Handle resource file validation errors
    const resourceFileFields: Record<string, string> = {
      ResourceImage: 'Resource Image',
      ResourceImagePath: 'Resource Image',
      ResourceLogo: 'Resource Logo',
      ResourceLogoPath: 'Resource Logo',
    };

    // Process resource file errors
    Object.keys(resourceFileFields).forEach((key) => {
      if (validationErrors[key]) {
        this.messageService.add({
          severity: 'error',
          summary: 'Validation Error',
          detail: key.endsWith('Path')
            ? `${resourceFileFields[key]} is required`
            : validationErrors[key][0],
        });
      }
    });

    // Handle other validation errors
    for (const key in validationErrors) {
      if (!Object.keys(resourceFileFields).includes(key)) {
        this.messageService.add({
          severity: 'error',
          summary: 'Validation Error',
          detail: validationErrors[key][0],
        });
      }
    }
  }

  prepareFormData(): FormData {
    const basicInfo = this.resourceForm.get('basicInfo')?.value;
    const contactDetails = this.resourceForm.get('contactDetails')?.value;
    const socialMedia = this.resourceForm.get('socialMedia')?.value;

    // Filter valid services
    const serviceValues = this.servicesArray.controls.map((c) => c.value);
    const services = serviceValues.filter(
      (service: string) =>
        service && typeof service === 'string' && service.trim().length > 0,
    );

    if (services.length === 0) {
      services.push('Default Service');
    }

    const formData = new FormData();

    // Add basic info fields
    const basicInfoFields = {
      OrganizationTitle: basicInfo.organizationTitle,
      SubTitle: basicInfo.subTitle || '',
      ResourceCategory: basicInfo.resourceCategory,
      Address: basicInfo.address,
      City: basicInfo.city,
      State: basicInfo.state,
      ZipCode: basicInfo.zipCode,
      ShortDescription: basicInfo.shortDescription,
      LongDescription: basicInfo.longDescription,
      Type: basicInfo.type.toString(),
    };

    Object.entries(basicInfoFields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });

    // Add services
    services.forEach((service, index) => {
      formData.append(`Services[${index}]`, service);
    });

    // Add contact details
    const contactDetailsFields = {
      'ContactDetails.ContactName': contactDetails.contactName || '',
      'ContactDetails.ContactNo': contactDetails.contactNo || '',
      'ContactDetails.Email': contactDetails.email || '',
      'ContactDetails.Website': contactDetails.website || '',
    };

    Object.entries(contactDetailsFields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });

    // Add social media
    const socialMediaFields = {
      'SocialMedia.Facebook': socialMedia.facebook || '',
      'SocialMedia.Twitter': socialMedia.twitter || '',
      'SocialMedia.Instagram': socialMedia.instagram || '',
      'SocialMedia.LinkedIn': socialMedia.linkedIn || '',
    };

    Object.entries(socialMediaFields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });

    // Add ID if in edit mode
    if (this.isEditMode && this.resourceId) {
      formData.append('Id', this.resourceId.toString());
    }

    // Handle resource paths and files
    this.appendResourcePaths(formData);
    this.appendResourceFiles(formData);

    return formData;
  }

  appendResourcePaths(formData: FormData): void {
    // Handle ResourceImagePath
    if (
      this.isEditMode &&
      this.resourceImagePreview &&
      !this.resourceImagePreview.startsWith('data:')
    ) {
      formData.append('ResourceImagePath', this.resourceImagePreview);
    } else {
      formData.append('ResourceImagePath', 'placeholder.jpg');
    }

    // Handle ResourceLogoPath
    if (
      this.isEditMode &&
      this.resourceLogoPreview &&
      !this.resourceLogoPreview.startsWith('data:')
    ) {
      formData.append('ResourceLogoPath', this.resourceLogoPreview);
    } else {
      formData.append('ResourceLogoPath', 'placeholder.jpg');
    }
  }

  appendResourceFiles(formData: FormData): void {
    if (this.resourceImage) {
      formData.append('ResourceImage', this.resourceImage);
    }

    if (this.resourceLogo) {
      formData.append('ResourceLogo', this.resourceLogo);
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl as FormGroup);
          } else {
            arrayControl.markAsTouched();
          }
        });
      }
    });
  }

  getFormValidationErrors(): any {
    const formErrors: any = {};

    Object.keys(this.resourceForm.controls).forEach((key) => {
      const controlErrors = this.resourceForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }

      // Check nested form groups
      const control = this.resourceForm.get(key);
      if (control instanceof FormGroup) {
        const nestedErrors = this.getNestedFormErrors(control, key);
        if (Object.keys(nestedErrors).length > 0) {
          formErrors[key] = { ...formErrors[key], ...nestedErrors };
        }
      }

      // Check form arrays
      if (control instanceof FormArray) {
        const arrayErrors = this.getFormArrayErrors(control, key);
        if (Object.keys(arrayErrors).length > 0) {
          formErrors[key] = arrayErrors;
        }
      }
    });

    return formErrors;
  }

  getNestedFormErrors(formGroup: FormGroup, parentKey: string): any {
    const formErrors: any = {};

    Object.keys(formGroup.controls).forEach((key) => {
      const controlErrors = formGroup.get(key)?.errors;
      if (controlErrors) {
        formErrors[`${parentKey}.${key}`] = controlErrors;
      }
    });

    return formErrors;
  }

  getFormArrayErrors(formArray: FormArray, parentKey: string): any {
    const formErrors: any = {};

    formArray.controls.forEach((control, index) => {
      const controlErrors = control.errors;
      if (controlErrors) {
        formErrors[`${parentKey}[${index}]`] = controlErrors;
      }
    });

    return formErrors;
  }

  validateWebsiteUrl(): boolean {
    const websiteControl = this.resourceForm.get('contactDetails.website');

    if (websiteControl?.value && websiteControl?.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Website URL Error',
        detail: 'Please enter a valid website URL (e.g., https://example.com)',
      });
      return false;
    }

    return true;
  }

  validateSocialMediaUrls(): boolean {
    this.socialMediaErrors = [];

    const socialMediaGroup = this.resourceForm.get('socialMedia');
    if (!socialMediaGroup) return true;

    const socialMediaFields = [
      {
        control: socialMediaGroup.get('facebook'),
        name: 'Facebook',
        format:
          'facebook.com/yourpage, www.facebook.com/yourpage, or https://facebook.com/yourpage',
      },
      {
        control: socialMediaGroup.get('instagram'),
        name: 'Instagram',
        format:
          'instagram.com/yourhandle, www.instagram.com/yourhandle, or https://instagram.com/yourhandle',
      },
      {
        control: socialMediaGroup.get('twitter'),
        name: 'Twitter',
        format:
          'twitter.com/yourhandle, www.twitter.com/yourhandle, x.com/yourhandle, or with https://',
      },
      {
        control: socialMediaGroup.get('linkedIn'),
        name: 'LinkedIn',
        format:
          'linkedin.com/in/yourprofile, www.linkedin.com/in/yourprofile, or with https://',
      },
    ];

    let hasErrors = false;

    socialMediaFields.forEach((field) => {
      if (field.control?.value && field.control?.invalid) {
        const errorMsg = `${field.name} URL must be in the format: ${field.format}`;
        this.socialMediaErrors.push(errorMsg);
        this.messageService.add({
          severity: 'error',
          summary: `${field.name} URL Error`,
          detail: errorMsg,
        });
        hasErrors = true;
      }
    });

    if (hasErrors) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return false;
    }

    return true;
  }
}
